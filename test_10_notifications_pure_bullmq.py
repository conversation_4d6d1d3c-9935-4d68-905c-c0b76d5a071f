#!/usr/bin/env python3
"""
Test 10 Notifications - Pure BullMQ
Test 10 notifications với Bull<PERSON> thuần, không qua wrapper
"""

import asyncio
import os
from datetime import datetime
from bullmq import Queue, Worker, Job
from bullmq.types import QueueBaseOptions, WorkerOptions

# Redis connection options
connection_opts = {
    "host": "redis-19926.c1.asia-northeast1-1.gce.cloud.redislabs.com",
    "port": 19926,
    "username": "default",
    "password": "X4vtYIZ5UE7CIACfqCxtm67kK1GX4S6J"
}


async def test_10_notifications_pure_bullmq():
    """Test 10 notifications with pure BullMQ"""
    print("🚀 Test 10 Notifications - Pure BullMQ")
    print("=" * 50)
    
    try:
        # Create queue and worker options
        queue_opts = QueueBaseOptions(connection=connection_opts)
        
        # Create queue
        print("📦 Creating queue...")
        queue = Queue("foxy-ai-service-notification", queue_opts)
        print("✅ Queue created")
        
        job_ids = []
        for i in range(10):
            delay_seconds = i * 10  # 0, 10, 20, ..., 90 seconds
            delay_ms = delay_seconds * 1000
            
            job_data = {
                'notification_id': f'pure_{i+1:02d}',
                'user_id': f'user_{i+1:03d}',
                'title': f'🔔 Pure BullMQ Notification #{i+1}',
                'content': f'This is notification {i+1} with {delay_seconds}s delay',
                'delay_seconds': delay_seconds
            }
            
            # Add job with delay
            job_options = {}
            if delay_ms > 0:
                job_options['delay'] = delay_ms
            
            job = await queue.add(f"notification-{i+1}", job_data, job_options)
            job_ids.append(job.id)
            
            if delay_seconds > 0:
                send_time = datetime.utcnow().timestamp() + delay_seconds
                send_str = datetime.fromtimestamp(send_time).strftime('%H:%M:%S')
                print(f"   ✅ {job_data['notification_id']} -> {send_str} (delay: {delay_seconds}s)")
            else:
                print(f"   ✅ {job_data['notification_id']} -> immediate")
        
        print(f"\n📊 Added {len(job_ids)} notifications to queue")
        
        # Start processing
        print(f"\n🔄 Starting processing...")
        print(f"⏱️ Expected duration: ~100 seconds")
        print(f"🕐 Start time: {datetime.utcnow().strftime('%H:%M:%S')}")
        
        # Monitor for 110 seconds
        print(f"\n📊 Monitoring progress:")
        print(f"{'Time':<8} {'Processed':<9}")
        print("-" * 25)
        
        start_time = datetime.utcnow()
        processed_count = 0
        
        for second in range(110):
            current_time = datetime.utcnow().strftime('%H:%M:%S')
            elapsed = (datetime.utcnow() - start_time).total_seconds()
            
            # Print status every 10 seconds
            if second % 10 == 0:
                print(f"{current_time:<8} {processed_count:>2d}/10")
            
            # Check if all processed (this is a simple approximation)
            if processed_count >= 10:
                print(f"\n🎉 All 10 notifications processed after {elapsed:.1f} seconds!")
                break
            
            await asyncio.sleep(1)
        
        # Cleanup
        print(f"\n🧹 Cleaning up...")
        
        print(f"✅ Pure BullMQ test completed!")
        return True
        
    except Exception as e:
        print(f"❌ Error during test: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Run all tests"""
    print("🚀 Pure BullMQ Tests")
    print("=" * 30)
    # Test 10 notifications
    success2 = await test_10_notifications_pure_bullmq()


if __name__ == "__main__":
    asyncio.run(main())
