import schedule
import os
import json
import asyncio
from datetime import datetime
from bullmq import Queue, Worker, Job
from bullmq.types import QueueBaseOptions, WorkerOptions
from utils.mgdatabase import (
    MongoDBManager, 
    create_db_manager, 
    get_default_user_data, 
    get_default_preferences
)
from utils.utils import UserContext
mgdb = MongoDBManager()
def create_information_user(list_information):
    list_user_infor: list[UserContext] = []
    for document in list_information:
        list_user_infor.append(UserContext(**document))
    return list_user_infor

def create_job_recommender_system():
    
    # create a job recommender system
    # get information user
    all_user = mgdb.get_all_user()
    print()

if __name__== "__main__":
    create_job_recommender_system()
